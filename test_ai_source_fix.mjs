#!/usr/bin/env deno run --allow-all

/**
 * 测试AI源修复后的实现
 */

import { getAISourcesManager } from './lib/AIsources_manager.mjs'

async function testAISourceFix() {
	console.log('🧪 测试AI源修复后的实现...\n')

	const manager = getAISourcesManager()

	try {
		// 测试加载sfw AI源（使用proxy生成器）
		console.log('📁 测试加载sfw AI源...')
		const sfwSource = await manager.loadAIsource('sfw.json')
		
		if (sfwSource) {
			console.log(`✅ 成功加载AI源: ${sfwSource.name}`)
			console.log(`   类型: ${sfwSource.config.type}`)
			console.log(`   模型: ${sfwSource.config.model}`)
			console.log(`   端点: ${sfwSource.config.endpoint}`)
			console.log(`   API密钥: ${sfwSource.config.apiKey ? '已配置' : '未配置'}`)
			console.log(`   fount生成器: ${sfwSource.fountGenerator ? '已加载' : '未加载'}`)
			
			if (sfwSource.fountGenerator) {
				console.log('\n🔧 测试fount生成器调用...')
				try {
					// 测试简单调用
					const testResult = await sfwSource.fountGenerator.Call('Hello, this is a test.')
					console.log(`✅ fount生成器调用成功`)
					console.log(`   响应内容: ${testResult.content?.substring(0, 100)}...`)
				} catch (error) {
					console.log(`❌ fount生成器调用失败: ${error?.message || error}`)
					console.log(`   错误详情: ${error?.stack || 'No stack trace available'}`)
					console.log(`   错误类型: ${typeof error}`)
					console.log(`   错误对象: ${JSON.stringify(error, null, 2)}`)
				}
			}
		} else {
			console.log('❌ 加载sfw AI源失败')
		}

		console.log('\n' + '='.repeat(50) + '\n')

		// 测试加载expert AI源（使用proxy生成器）
		console.log('📁 测试加载expert AI源...')
		const expertSource = await manager.loadAIsource('expert.json')
		
		if (expertSource) {
			console.log(`✅ 成功加载AI源: ${expertSource.name}`)
			console.log(`   类型: ${expertSource.config.type}`)
			console.log(`   模型: ${expertSource.config.model}`)
			console.log(`   端点: ${expertSource.config.endpoint}`)
			console.log(`   API密钥: ${expertSource.config.apiKey ? '已配置' : '未配置'}`)
			console.log(`   fount生成器: ${expertSource.fountGenerator ? '已加载' : '未加载'}`)
			
			if (expertSource.fountGenerator) {
				console.log('\n🔧 测试fount生成器调用...')
				try {
					// 测试简单调用
					const testResult = await expertSource.fountGenerator.Call('Hello, this is a test.')
					console.log(`✅ fount生成器调用成功`)
					console.log(`   响应内容: ${testResult.content?.substring(0, 100)}...`)
				} catch (error) {
					console.log(`❌ fount生成器调用失败: ${error?.message || error}`)
					console.log(`   错误详情: ${error?.stack || 'No stack trace available'}`)
					console.log(`   错误类型: ${typeof error}`)
					console.log(`   错误对象: ${JSON.stringify(error, null, 2)}`)

					// 检查是否是URL undefined的问题
					const errorStr = String(error?.message || error || '')
					if (errorStr.includes('Invalid URL') || errorStr.includes('undefined')) {
						console.log('🔍 检测到URL undefined问题，这是我们要修复的核心问题')
					}
				}
			}
		} else {
			console.log('❌ 加载expert AI源失败')
		}

		console.log('\n' + '='.repeat(50) + '\n')

		// 测试OrderedAISourceCalling
		console.log('🎯 测试OrderedAISourceCalling...')
		try {
			const { OrderedAISourceCalling } = await import('./AISource/index.mjs')
			
			const result = await OrderedAISourceCalling(
				'sfw',
				async (source) => {
					console.log(`   尝试调用AI源: ${source.name || 'unknown'}`)
					if (source.fountGenerator) {
						const response = await source.fountGenerator.Call('Hello, this is a test from OrderedAISourceCalling.')
						return response.content
					} else {
						throw new Error('No fount generator available')
					}
				},
				1, // 只尝试1次
				async (err) => {
					console.log(`   ⚠️ AI源调用失败: ${err.message}`)
				}
			)
			
			console.log(`✅ OrderedAISourceCalling成功`)
			console.log(`   响应内容: ${result.content?.substring(0, 100)}...`)
		} catch (error) {
			console.log(`❌ OrderedAISourceCalling失败: ${error.message}`)
			console.log(`   错误详情: ${error.stack}`)
		}

	} catch (error) {
		console.error('❌ 测试过程中发生错误:', error.message)
		console.error('错误详情:', error.stack)
	}
}

// 运行测试
if (import.meta.main) {
	await testAISourceFix()
}
