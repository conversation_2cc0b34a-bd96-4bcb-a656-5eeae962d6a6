#!/usr/bin/env deno run --allow-all

/**
 * 测试真实配置文件的AI源调用
 */

import { getAISourcesManager } from './lib/AIsources_manager.mjs'

console.log('🧪 测试真实配置文件的AI源调用...')

const manager = getAISourcesManager()

try {
	// 测试加载sfw AI源
	console.log('📁 加载sfw AI源...')
	const sfwSource = await manager.loadAIsource('sfw.json')
	
	if (sfwSource && sfwSource.fountGenerator) {
		console.log(`✅ 成功加载AI源: ${sfwSource.name}`)
		console.log(`   类型: ${sfwSource.config.type}`)
		console.log(`   模型: ${sfwSource.config.model}`)
		console.log(`   端点: ${sfwSource.config.endpoint}`)
		console.log(`   API密钥: ${sfwSource.config.apiKey ? '已配置' : '未配置'}`)
		
		// 测试fount生成器调用
		console.log('\n🔧 测试fount生成器调用...')
		try {
			const testResult = await sfwSource.fountGenerator.Call('Hello, this is a test.')
			console.log(`✅ fount生成器调用成功`)
			console.log(`   响应内容: ${testResult.content?.substring(0, 200)}...`)
		} catch (error) {
			console.log(`❌ fount生成器调用失败: ${error?.message || error}`)
			
			// 检查错误类型
			if (error?.status === 401) {
				console.log('🔍 这是API密钥认证错误，说明URL构造正确，但API密钥可能无效')
			} else if (error?.message?.includes('Invalid URL') || error?.message?.includes('undefined')) {
				console.log('🔍 这是URL undefined问题，需要进一步修复')
			} else {
				console.log('🔍 这是其他类型的错误')
			}
			
			console.log(`   错误详情: ${error?.stack || 'No stack trace'}`)
		}
	} else {
		console.log('❌ 加载sfw AI源失败或未找到fount生成器')
	}

	console.log('\n' + '='.repeat(50) + '\n')

	// 测试加载expert AI源
	console.log('📁 加载expert AI源...')
	const expertSource = await manager.loadAIsource('expert.json')
	
	if (expertSource && expertSource.fountGenerator) {
		console.log(`✅ 成功加载AI源: ${expertSource.name}`)
		console.log(`   类型: ${expertSource.config.type}`)
		console.log(`   模型: ${expertSource.config.model}`)
		console.log(`   端点: ${expertSource.config.endpoint}`)
		console.log(`   API密钥: ${expertSource.config.apiKey ? '已配置' : '未配置'}`)
		
		// 测试fount生成器调用
		console.log('\n🔧 测试fount生成器调用...')
		try {
			const testResult = await expertSource.fountGenerator.Call('Hello, this is a test.')
			console.log(`✅ fount生成器调用成功`)
			console.log(`   响应内容: ${testResult.content?.substring(0, 200)}...`)
		} catch (error) {
			console.log(`❌ fount生成器调用失败: ${error?.message || error}`)
			
			// 检查错误类型
			if (error?.status === 401) {
				console.log('🔍 这是API密钥认证错误，说明URL构造正确，但API密钥可能无效')
			} else if (error?.message?.includes('Invalid URL') || error?.message?.includes('undefined')) {
				console.log('🔍 这是URL undefined问题，需要进一步修复')
			} else {
				console.log('🔍 这是其他类型的错误')
			}
			
			console.log(`   错误详情: ${error?.stack || 'No stack trace'}`)
		}
	} else {
		console.log('❌ 加载expert AI源失败或未找到fount生成器')
	}

} catch (error) {
	console.error('❌ 测试过程中发生错误:', error?.message || error)
	console.error('   错误详情:', error?.stack || 'No stack trace')
}

console.log('\n🏁 测试完成')
