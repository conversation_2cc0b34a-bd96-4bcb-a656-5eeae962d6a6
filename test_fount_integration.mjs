#!/usr/bin/env deno run --allow-all

/**
 * 测试fount AI源生成器集成
 */

import { getAISourcesManager } from './lib/AIsources_manager.mjs'

async function testFountIntegration() {
	console.log('🧪 开始测试fount AI源生成器集成...\n')

	const manager = getAISourcesManager()

	try {
		// 测试加载AI源
		console.log('📁 测试加载AI源配置...')
		const expertSource = await manager.loadAIsource('expert.json')
		
		if (expertSource) {
			console.log(`✅ 成功加载AI源: ${expertSource.name}`)
			console.log(`   类型: ${expertSource.config.type}`)
			console.log(`   模型: ${expertSource.config.model}`)
			console.log(`   fount生成器: ${expertSource.fountGenerator ? '已加载' : '未加载'}`)
		} else {
			console.log('❌ 加载AI源失败')
		}

		// 测试Gemini配置（如果存在）
		console.log('\n📁 测试加载Gemini AI源...')
		const geminiSource = await manager.loadAIsource('gemini.json')
		
		if (geminiSource) {
			console.log(`✅ 成功加载Gemini AI源: ${geminiSource.name}`)
			console.log(`   类型: ${geminiSource.config.type}`)
			console.log(`   模型: ${geminiSource.config.model}`)
			console.log(`   fount生成器: ${geminiSource.fountGenerator ? '已加载' : '未加载'}`)
		} else {
			console.log('⚠️  Gemini AI源未配置或已禁用')
		}

		// 列出所有已加载的AI源
		console.log('\n📋 已加载的AI源列表:')
		const sources = manager.listSources()
		for (const filename of sources) {
			const source = manager.getSource(filename)
			if (source) {
				console.log(`   - ${source.name} (${source.config.type})`)
			}
		}

		// 测试AI源信息获取
		console.log('\n📊 测试AI源信息获取...')
		for (const filename of sources) {
			const info = manager.getAISourceInfo(filename)
			if (info) {
				console.log(`   ${info.name}: ${info.status.status}`)
			}
		}

		console.log('\n✅ fount AI源生成器集成测试完成!')

	} catch (error) {
		console.error('❌ 测试过程中发生错误:', error.message)
		console.error(error.stack)
	}
}

// 运行测试
if (import.meta.main) {
	await testFountIntegration()
}
