/**
 * 字符串转义工具函数
 * 用于正则表达式和其他字符串处理
 */

/**
 * 转义正则表达式特殊字符
 * @param {string} string - 需要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeRegExp(string) {
	if (typeof string !== 'string') {
		return ''
	}
	return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 转义HTML特殊字符
 * @param {string} string - 需要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeHtml(string) {
	if (typeof string !== 'string') {
		return ''
	}
	const htmlEscapes = {
		'&': '&amp;',
		'<': '&lt;',
		'>': '&gt;',
		'"': '&quot;',
		"'": '&#39;'
	}
	return string.replace(/[&<>"']/g, (match) => htmlEscapes[match])
}

/**
 * 转义JSON字符串
 * @param {string} string - 需要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeJson(string) {
	if (typeof string !== 'string') {
		return ''
	}
	return string.replace(/\\/g, '\\\\')
		.replace(/"/g, '\\"')
		.replace(/\n/g, '\\n')
		.replace(/\r/g, '\\r')
		.replace(/\t/g, '\\t')
}

/**
 * 反转义字符串
 * @param {string} string - 需要反转义的字符串
 * @returns {string} 反转义后的字符串
 */
export function unescapeString(string) {
	if (typeof string !== 'string') {
		return ''
	}
	return string.replace(/\\(.)/g, '$1')
}
