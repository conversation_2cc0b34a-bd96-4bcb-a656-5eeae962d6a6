#!/usr/bin/env deno run --allow-all

/**
 * 直接测试proxy生成器
 */

console.log('🧪 直接测试proxy生成器...')

try {
	console.log('📦 测试基础导入...')
	
	// 测试escapeRegExp函数
	const { escapeRegExp } = await import('./scripts/escape.mjs')
	console.log('✅ escapeRegExp导入成功')
	
	// 测试prompt_struct函数
	const { margeStructPromptChatLog, structPromptToSingleNoChatLog } = await import('./lib/prompt_struct.mjs')
	console.log('✅ prompt_struct导入成功')
	
	// 现在测试proxy生成器
	console.log('📦 导入proxy生成器...')
	const proxyModule = await import('./lib/fount_ai_generators/proxy/main.mjs')
	console.log('✅ proxy生成器导入成功')

	// 测试配置模板
	console.log('📋 获取配置模板...')
	const template = await proxyModule.default.interfaces.AIsource.GetConfigTemplate()
	console.log('✅ 配置模板:', JSON.stringify(template, null, 2))

	// 测试配置映射
	console.log('🔧 测试配置映射...')
	const testConfig = {
		name: 'test-proxy',
		endpoint: 'https://tbai.xin/v1/chat/completions',
		apiKey: 'test-key',
		model: 'gpt-3.5-turbo',
		parameters: {
			temperature: 0.7,
			max_tokens: 1000
		}
	}

	console.log('📥 输入配置:', JSON.stringify(testConfig, null, 2))

	const source = await proxyModule.default.interfaces.AIsource.GetSource(testConfig, {
		SaveConfig: () => console.log('SaveConfig called')
	})

	console.log('✅ AI源创建成功')
	console.log('📤 AI源信息:', JSON.stringify(source.info, null, 2))

	// 测试简单调用（这里会失败，但我们可以看到错误信息）
	console.log('🔧 测试AI源调用...')
	try {
		const result = await source.Call('Hello')
		console.log('✅ 调用成功:', result)
	} catch (error) {
		console.log('❌ 调用失败 (预期的):', error?.message || error)
		console.log('   错误类型:', typeof error)
		console.log('   错误详情:', error?.stack || 'No stack')
		
		// 检查是否是URL undefined的问题
		const errorStr = String(error?.message || error || '')
		if (errorStr.includes('Invalid URL') || errorStr.includes('undefined')) {
			console.log('🔍 检测到URL undefined问题，这是我们要修复的核心问题')
		}
	}

} catch (error) {
	console.error('❌ 测试失败:', error?.message || error)
	console.error('   错误详情:', error?.stack || 'No stack')
}

console.log('🏁 测试完成')
