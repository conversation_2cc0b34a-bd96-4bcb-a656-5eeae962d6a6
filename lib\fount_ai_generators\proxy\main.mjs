/**
 * OpenAI兼容代理AI源生成器 - 基于fount平台实现
 * 适配GentianAphrodite项目的Deno + ESM6+ + MJS格式
 */

import { margeStructPromptChatLog, structPromptToSingleNoChatLog } from '../../prompt_struct.mjs'
import { escapeRegExp } from '../../../scripts/escape.mjs'
/** @typedef {import('../../types/AIsource.mjs').AIsource_t} AIsource_t */
/** @typedef {import('../../prompt_struct.mjs').prompt_struct_t} prompt_struct_t */

export default {
	interfaces: {
		AIsource: {
			GetConfigTemplate: async () => configTemplate,
			GetSource,
		}
	}
}

const configTemplate = {
	name: 'openai-proxy',
	url: 'https://api.openai.com/v1/chat/completions',
	model: 'gpt-3.5-turbo',
	apikey: '',
	model_arguments: {
		temperature: 1,
		n: 1
	},
	convert_config: {
		passName: false,
		roleReminding: true
	}
}

async function GetSource(config, { SaveConfig }) {
	// 参数映射：将GentianAphrodite配置格式转换为fount格式
	const fountConfig = {
		name: config.name || 'openai-proxy',
		url: config.endpoint || config.url || 'https://api.openai.com/v1/chat/completions',
		model: config.model || 'gpt-3.5-turbo',
		apikey: config.apiKey || config.apikey || '',
		model_arguments: {
			temperature: config.parameters?.temperature || config.model_arguments?.temperature || 1,
			n: config.parameters?.n || config.model_arguments?.n || 1,
			max_tokens: config.parameters?.max_tokens || config.maxTokens || config.model_arguments?.max_tokens,
			top_p: config.parameters?.top_p || config.model_arguments?.top_p,
			frequency_penalty: config.parameters?.frequency_penalty || config.model_arguments?.frequency_penalty || 0,
			presence_penalty: config.parameters?.presence_penalty || config.model_arguments?.presence_penalty || 0
		},
		convert_config: config.convert_config || {
			passName: false,
			roleReminding: true
		},
		system_prompt_at_depth: config.system_prompt_at_depth
	}

	async function callBase(messages, config) {
		let text
		let files = []
		while (!text && !files.length) {
			const result = await fetch(config.url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': config.apikey ? 'Bearer ' + config.apikey : undefined
				},
				body: JSON.stringify({
					model: config.model,
					messages,
					stream: false,
					...config.model_arguments,
				})
			})

			if (!result.ok)
				throw result

			text = await result.text()
			if (text.startsWith('data:'))
				text = text.split('\n').filter((line) => line.startsWith('data:')).map(line => line.slice(5).trim()).map(JSON.parse).map((json) => json.choices[0].delta.content).join('')
			else {
				let json
				try { json = JSON.parse(text) }
				catch { json = await result.json() }
				text = json.choices[0].message.content
				let imgindex = 0
				files = (await Promise.all(json.choices[0].message?.images?.map?.(async (imageurl) => ({
					name: `image${imgindex++}.png`,
					buffer: await (await fetch(imageurl)).arrayBuffer(),
					mimetype: 'image/png'
				})) || [])).filter(Boolean)
			}
		}
		return {
			content: text,
			files,
		}
	}

	async function callBaseEx(messages) {
		const errors = []
		let retryConfigs = [
			{}, // 第一次尝试，使用原始配置
			{ urlSuffix: '/v1/chat/completions' },
			{ urlSuffix: '/chat/completions' },
		]
		if (fountConfig.url.endsWith('/chat/completions'))
			retryConfigs = retryConfigs.filter((config) => !config?.urlSuffix?.endsWith?.('/chat/completions'))

		for (const retryConfig of retryConfigs) {
			const currentConfig = { ...fountConfig } // 复制配置，避免修改原始配置
			if (retryConfig.urlSuffix)
				currentConfig.url += retryConfig.urlSuffix

			try {
				const result = await callBase(messages, currentConfig)

				if (retryConfig.urlSuffix)
					console.warn(`the api url of ${fountConfig.model} need to change from ${fountConfig.url} to ${currentConfig.url}`)

				if (retryConfig.urlSuffix) {
					Object.assign(fountConfig, currentConfig)
					SaveConfig()
				}

				return result
			} catch (error) {
				errors.push(error)
			}
		}
		// 如果只有一个错误，直接抛出；如果有多个错误，创建一个包含所有错误信息的新错误
		if (errors.length === 1) {
			throw errors[0]
		} else {
			const combinedError = new Error(`All retry attempts failed. Errors: ${errors.map(e => e?.message || e).join('; ')}`)
			combinedError.originalErrors = errors
			throw combinedError
		}
	}

	/** @type {AIsource_t} */
	const result = {
		type: 'text-chat',
		info: {
			'': {
				avatar: '',
				name: fountConfig.name || fountConfig.model,
				provider: fountConfig.provider || 'unknown',
				description: 'proxy',
				description_markdown: 'proxy',
				version: '0.0.0',
				author: 'steve02081504',
				homepage: '',
				tags: ['proxy'],
			}
		},
		is_paid: false,
		extension: {},

		Unload: () => { },
		Call: async (prompt) => {
			return await callBaseEx([
				{
					role: 'system',
					content: prompt
				}
			])
		},
		StructCall: async (/** @type {prompt_struct_t} */ prompt_struct) => {
			const messages = []
			margeStructPromptChatLog(prompt_struct).forEach((chatLogEntry) => {
				messages.push({
					role: chatLogEntry.role === 'user' ? 'user' : chatLogEntry.role === 'system' ? 'system' : 'assistant',
					content: fountConfig.convert_config?.passName ? chatLogEntry.content : chatLogEntry.name + ':\n' + chatLogEntry.content,
					name: fountConfig.convert_config?.passName ? chatLogEntry.name : undefined
				})
			})

			const system_prompt = structPromptToSingleNoChatLog(prompt_struct)
			if (fountConfig.system_prompt_at_depth ?? 10)
				messages.splice(Math.max(messages.length - (fountConfig.system_prompt_at_depth ?? 10), 0), 0, {
					role: 'system',
					content: system_prompt
				})
			else
				messages.unshift({
					role: 'system',
					content: system_prompt
				})

			if (fountConfig.convert_config?.roleReminding ?? true) {
				const isMutiChar = new Set([...prompt_struct.chat_log.map((chatLogEntry) => chatLogEntry.name).filter(Boolean)]).size > 2
				if (isMutiChar)
					messages.push({
						role: 'system',
						content: `现在请以${prompt_struct.Charname}的身份续写对话。`
					})
			}

			const result = await callBaseEx(messages)

			let text = result.content

			{
				text = text.split('\n')
				const base_reg = `^((|${[...new Set([
					prompt_struct.Charname,
					...prompt_struct.chat_log.map((chatLogEntry) => chatLogEntry.name),
				])].filter(Boolean).map(escapeRegExp).concat([
					...(prompt_struct.alternative_charnames || []).map(Object).map(
						(stringOrReg) => {
							if (stringOrReg instanceof String) return escapeRegExp(stringOrReg)
							return stringOrReg.source
						}
					),
				].filter(Boolean)).join('|')})[^\\n：:\<\>\\d\`]*)(:|：)\\s*(?!\/)`
				let reg = new RegExp(`${base_reg}$`, 'i')
				while (text[0].trim().match(reg)) text.shift()
				reg = new RegExp(`${base_reg}`, 'i')
				text[0] = text[0].replace(reg, '')
				text = text.join('\n')
			}

			return {
				...result,
				content: text
			}
		},
		Tokenizer: {
			free: () => 0,
			encode: (prompt) => prompt,
			decode: (tokens) => tokens,
			decode_single: (token) => token,
			get_token_count: (prompt) => prompt.length
		}
	}
	return result
}
