/**
 * OpenAI兼容代理AI源生成器 - 基于fount平台实现
 * 适配GentianAphrodite项目的Deno + ESM6+ + MJS格式
 */

import { margeStructPromptChatLog, structPromptToSingleNoChatLog } from '../../prompt_struct.mjs'
import { escapeRegExp } from '../../../scripts/escape.mjs'

/** @typedef {import('../../types/AIsource.mjs').AIsource_t} AIsource_t */
/** @typedef {import('../prompt_struct.mjs').prompt_struct_t} prompt_struct_t */

// 配置模板
const configTemplate = {
	name: 'openai-proxy',
	url: 'https://api.openai.com/v1/chat/completions',
	model: 'gpt-3.5-turbo',
	apikey: '',
	model_arguments: {
		temperature: 1,
		n: 1,
		max_tokens: 4000,
		top_p: 0.9,
		frequency_penalty: 0,
		presence_penalty: 0
	},
	convert_config: {
		passName: false,
		roleReminding: true
	}
}

/**
 * 获取AI源实例
 * @param {Object} config - 配置对象
 * @param {Object} options - 选项
 * @returns {Promise<AIsource_t>} AI源实例
 */
async function GetSource(config, { SaveConfig }) {
	/**
	 * 基础调用函数
	 * @param {Array} messages - 消息数组
	 * @param {Object} config - 配置
	 * @returns {Promise<Object>} 响应结果
	 */
	async function callBase(messages, config) {
		let text
		let files = []
		
		while (!text && !files.length) {
			const requestBody = {
				model: config.model,
				messages,
				stream: false,
				...config.model_arguments,
			}

			const headers = {
				'Content-Type': 'application/json'
			}

			// 添加授权头
			if (config.apikey) {
				headers['Authorization'] = 'Bearer ' + config.apikey
			}

			try {
				const result = await fetch(config.url, {
					method: 'POST',
					headers,
					body: JSON.stringify(requestBody)
				})

				if (!result.ok) {
					const errorText = await result.text()
					throw new Error(`HTTP ${result.status}: ${errorText}`)
				}

				const data = await result.json()
				
				if (!data.choices || !data.choices[0]) {
					throw new Error('Invalid response format from API')
				}

				text = data.choices[0].message?.content
				
				if (!text) {
					throw new Error('Empty response from API')
				}

				// 处理文件（如果有）
				if (data.choices[0].message?.files) {
					files = data.choices[0].message.files
				}

			} catch (error) {
				console.error('Proxy API error:', error)
				throw error
			}
		}

		return { text, files }
	}

	/**
	 * 转换消息格式
	 * @param {prompt_struct_t} prompt_struct - 提示词结构
	 * @returns {Array} 转换后的消息数组
	 */
	function convertMessages(prompt_struct) {
		const messages = []
		
		// 添加系统提示词
		const systemPrompt = structPromptToSingleNoChatLog(prompt_struct)
		if (systemPrompt) {
			messages.push({
				role: 'system',
				content: systemPrompt
			})
		}

		// 添加聊天历史
		const chatLog = margeStructPromptChatLog(prompt_struct)
		for (const entry of chatLog) {
			let role = 'user'
			if (entry.role === 'assistant') {
				role = 'assistant'
			} else if (entry.role === 'system') {
				role = 'system'
			}

			let content = entry.content
			if (config.convert_config?.passName && entry.name) {
				content = `${entry.name}: ${content}`
			}

			messages.push({
				role,
				content
			})
		}

		return messages
	}

	// 返回AI源接口
	return {
		// fount 平台兼容方法
		Unload: async () => { },
		Call: async (prompt) => {
			const messages = [{ role: 'user', content: prompt }]
			const result = await callBase(messages, config)
			return { content: result.text }
		},
		StructCall: async (prompt_struct) => {
			const messages = convertMessages(prompt_struct)
			const result = await callBase(messages, config)
			return { content: result.text, files: result.files || [] }
		},
		Tokenizer: {
			free: async () => { },
			encode: (prompt) => prompt.split(' '), // 简单实现
			decode: (tokens) => tokens.join(' '), // 简单实现
			decode_single: (token) => token,
			get_token_count: (prompt) => Math.ceil(prompt.length / 4), // 简单估算
		},

		// 原有方法兼容
		call: async (request) => {
			const messages = []
			if (request.prompt) {
				messages.push({ role: 'user', content: request.prompt })
			}
			if (request.messages) {
				messages.push(...request.messages)
			}
			const result = await callBase(messages, config)
			return {
				content: result.text,
				files: result.files || [],
				metadata: {
					source: 'openai-proxy',
					model: config.model
				},
				tokensUsed: 0, // TODO: 实现token计算
				model: config.model,
				timestamp: Date.now()
			}
		},
		test: async () => {
			try {
				const result = await callBase([{ role: 'user', content: 'Hello' }], config)
				return { success: true, message: 'Proxy API connection successful' }
			} catch (error) {
				return { success: false, message: error.message }
			}
		},
		getInfo: () => ({
			name: 'OpenAI Proxy',
			type: 'openai-proxy',
			model: config.model,
			status: 'ready'
		}),
		updateConfig: (newConfig) => {
			Object.assign(config, newConfig)
			SaveConfig(config)
		},
		reset: () => {
			// 重置状态
		}
	}
}

export default {
	interfaces: {
		AIsource: {
			GetConfigTemplate: async () => configTemplate,
			GetSource,
		}
	}
}
