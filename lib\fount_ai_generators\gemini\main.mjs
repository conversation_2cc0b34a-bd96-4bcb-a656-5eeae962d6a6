/**
 * Gemini AI源生成器 - 基于fount平台实现
 * 适配GentianAphrodite项目的Deno + ESM6+ + MJS格式
 */

import { escapeRegExp } from '../../../scripts/escape.mjs'
import { margeStructPromptChatLog, structPromptToSingleNoChatLog } from '../../prompt_struct.mjs'

/** @typedef {import('../../types/AIsource.mjs').AIsource_t} AIsource_t */
/** @typedef {import('../prompt_struct.mjs').prompt_struct_t} prompt_struct_t */

// 支持的文件类型
const supportedFileTypes = [
	'application/pdf',
	'application/x-javascript',
	'text/javascript',
	'application/x-python',
	'text/x-python',
	'text/plain',
	'text/html',
	'text/css',
	'text/md',
	'text/csv',
	'text/xml',
	'text/rtf',
	'image/png',
	'image/jpeg',
	'image/webp',
	'image/heic',
	'image/heif',
	'video/mp4',
	'video/mpeg',
	'video/mov',
	'video/avi',
	'video/x-flv',
	'video/mpg',
	'video/webm',
	'video/wmv',
	'video/3gpp',
	'audio/wav',
	'audio/mp3',
	'audio/aiff',
	'audio/aac',
	'audio/ogg',
	'audio/flac'
]

// 文件上传映射
const fileUploadMap = new Map()

// 配置模板
const configTemplate = {
	name: 'gemini',
	apiKey: '',
	model: 'gemini-1.5-flash',
	model_arguments: {
		temperature: 1,
		topP: 0.95,
		topK: 40,
		maxOutputTokens: 8192,
		responseMimeType: 'text/plain'
	},
	convert_config: {
		passName: false,
		roleReminding: true
	}
}

/**
 * 获取AI源实例
 * @param {Object} config - 配置对象
 * @param {Object} options - 选项
 * @returns {Promise<AIsource_t>} AI源实例
 */
async function GetSource(config, { SaveConfig }) {
	// 动态导入Gemini SDK
	let GoogleGenAI, HarmCategory, HarmBlockThreshold, createPartFromUri
	try {
		const genAI = await import('npm:@google/genai@^0.12.0')
		GoogleGenAI = genAI.GoogleGenAI
		HarmCategory = genAI.HarmCategory
		HarmBlockThreshold = genAI.HarmBlockThreshold
		createPartFromUri = genAI.createPartFromUri
	} catch (error) {
		throw new Error(`Failed to import Gemini SDK: ${error.message}`)
	}

	const genAI = new GoogleGenAI(config.apiKey)

	/**
	 * 基础调用函数
	 * @param {Array} messages - 消息数组
	 * @param {Object} config - 配置
	 * @returns {Promise<Object>} 响应结果
	 */
	async function callBase(messages, config) {
		let text
		let files = []

		while (!text && !files.length) {
			const model = genAI.getGenerativeModel({
				model: config.model,
				generationConfig: {
					temperature: config.model_arguments?.temperature || 1,
					topP: config.model_arguments?.topP || 0.95,
					topK: config.model_arguments?.topK || 40,
					maxOutputTokens: config.model_arguments?.maxOutputTokens || 8192,
					responseMimeType: config.model_arguments?.responseMimeType || 'text/plain'
				},
				safetySettings: [
					{
						category: HarmCategory.HARM_CATEGORY_HARASSMENT,
						threshold: HarmBlockThreshold.BLOCK_NONE,
					},
					{
						category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
						threshold: HarmBlockThreshold.BLOCK_NONE,
					},
					{
						category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
						threshold: HarmBlockThreshold.BLOCK_NONE,
					},
					{
						category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
						threshold: HarmBlockThreshold.BLOCK_NONE,
					},
				],
			})

			// 转换消息格式
			const parts = []
			for (const message of messages) {
				if (message.role === 'system' || message.role === 'user') {
					parts.push({ text: message.content })
				}
			}

			try {
				const result = await model.generateContent(parts)
				const response = await result.response
				text = response.text()

				if (!text) {
					throw new Error('Empty response from Gemini API')
				}
			} catch (error) {
				console.error('Gemini API error:', error)
				throw error
			}
		}

		return { text, files }
	}

	/**
	 * 转换消息格式
	 * @param {prompt_struct_t} prompt_struct - 提示词结构
	 * @returns {Array} 转换后的消息数组
	 */
	function convertMessages(prompt_struct) {
		const messages = []
		
		// 添加系统提示词
		const systemPrompt = structPromptToSingleNoChatLog(prompt_struct)
		if (systemPrompt) {
			messages.push({
				role: 'system',
				content: systemPrompt
			})
		}

		// 添加聊天历史
		const chatLog = margeStructPromptChatLog(prompt_struct)
		for (const entry of chatLog) {
			messages.push({
				role: entry.role === 'assistant' ? 'assistant' : 'user',
				content: `${entry.name}: ${entry.content}`
			})
		}

		return messages
	}

	// 返回AI源接口
	return {
		// fount 平台兼容方法
		Unload: async () => { },
		Call: async (prompt) => {
			const messages = [{ role: 'user', content: prompt }]
			const result = await callBase(messages, config)
			return { content: result.text }
		},
		StructCall: async (prompt_struct) => {
			const messages = convertMessages(prompt_struct)
			const result = await callBase(messages, config)
			return { content: result.text, files: result.files || [] }
		},
		Tokenizer: {
			free: async () => { },
			encode: (prompt) => prompt.split(' '), // 简单实现
			decode: (tokens) => tokens.join(' '), // 简单实现
			decode_single: (token) => token,
			get_token_count: (prompt) => Math.ceil(prompt.length / 4), // 简单估算
		},

		// 原有方法兼容
		call: async (request) => {
			const messages = []
			if (request.prompt) {
				messages.push({ role: 'user', content: request.prompt })
			}
			if (request.messages) {
				messages.push(...request.messages)
			}
			const result = await callBase(messages, config)
			return {
				content: result.text,
				files: result.files || [],
				metadata: {
					source: 'gemini',
					model: config.model
				},
				tokensUsed: 0, // TODO: 实现token计算
				model: config.model,
				timestamp: Date.now()
			}
		},
		test: async () => {
			try {
				const result = await callBase([{ role: 'user', content: 'Hello' }], config)
				return { success: true, message: 'Gemini API connection successful' }
			} catch (error) {
				return { success: false, message: error.message }
			}
		},
		getInfo: () => ({
			name: 'Gemini AI',
			type: 'gemini',
			model: config.model,
			status: 'ready'
		}),
		updateConfig: (newConfig) => {
			Object.assign(config, newConfig)
			SaveConfig(config)
		},
		reset: () => {
			// 重置状态
		}
	}
}

export default {
	interfaces: {
		AIsource: {
			GetConfigTemplate: async () => configTemplate,
			GetSource,
		}
	}
}
