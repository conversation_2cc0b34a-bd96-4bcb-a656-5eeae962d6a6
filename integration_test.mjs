/**
 * fount集成测试
 */

console.log('🧪 开始fount集成测试...')

try {
	// 测试导入
	console.log('📦 测试导入模块...')
	
	const { getAISourcesManager } = await import('./lib/AIsources_manager.mjs')
	console.log('✅ 成功导入AI源管理器')
	
	const manager = getAISourcesManager()
	console.log('✅ 成功获取管理器实例')
	
	// 测试配置加载
	console.log('📁 测试配置加载...')
	const expertSource = await manager.loadAIsource('expert.json')
	
	if (expertSource) {
		console.log(`✅ 成功加载: ${expertSource.name}`)
		console.log(`   类型: ${expertSource.config.type}`)
		console.log(`   fount生成器: ${expertSource.fountGenerator ? '已加载' : '未加载'}`)
	} else {
		console.log('❌ 加载失败')
	}
	
	console.log('✅ 测试完成!')
	
} catch (error) {
	console.error('❌ 测试失败:', error.message)
	console.error(error.stack)
}
